<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yunzheng</groupId>
        <artifactId>rd-scaffold</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>server</artifactId>

    <dependencies>
        <!--通用能力依赖-->
        <dependency>
            <groupId>com.yunzheng.capability</groupId>
            <artifactId>login</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.yunzheng.capability</groupId>
            <artifactId>sysconfig</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunzheng.capability</groupId>
            <artifactId>registration</artifactId>
        </dependency>


        <dependency>
            <groupId>com.yunzheng.capability</groupId>
            <artifactId>audit-log</artifactId>
        </dependency>

        <!--内置依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunzheng.capability</groupId>
            <artifactId>graphql</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunzheng.capability</groupId>
            <artifactId>openapi</artifactId>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <finalName>server</finalName>
    </build>

</project>