server:
  port: 9528
  servlet:
    context-path: /rd-platform
spring:
  application:
    name: rd-platform
  datasource:
    url: *****************************************
    username: hasura
    password: hasura
    driver-class-name: org.postgresql.Driver
    hikari:
      schema: public
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  builtin:
    super:
      admin:
        # 是否启用内置超管账号
        enabled: true
        # 超管用户名（建议通过环境变量设置）
        username: ${BUILTIN_SUPER_ADMIN_USERNAME:super@Admin}
        # 超管密码（建议通过环境变量设置）
        password: ${BUILTIN_SUPER_ADMIN_PASSWORD:af81527ff39d63b7b78dddfaa2e6b545}

# 权限配置
permission:
  # 开发环境默认给所有登录用户全部菜单权限
  default:
    all-menu-access: true
    # 是否跳过权限检查
    skip-permission-check: true

hasura:
  server:
    url: http://127.0.0.1:8080
  endpoint: http://127.0.0.1:8080
  admin:
    secret: yzyl
