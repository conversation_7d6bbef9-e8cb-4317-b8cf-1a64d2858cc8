server:
  port: 9528
  servlet:
    context-path: /${spring.application.name}
spring:
  application:
    name: rd-scaffold
  config:
    import: classpath:application-generated.yml
  datasource:
    url: ${DATABASE_URL:***********************************************}
    username: ${POLARDB_USER:postgres}
    password: ${POLARDB_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.PostgreSQLDialect
logging:
  config: classpath:logback-prod.xml

hasura:
  server:
    url: http://graphql-engine:8080
    admin:
      secret: 1234

login:
  ignoreUrl: /api/health/check